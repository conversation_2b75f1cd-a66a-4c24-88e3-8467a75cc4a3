package com.example.splitexpenses.ui.navigation

import androidx.navigation.NavController
import androidx.navigation.NavOptionsBuilder

/**
 * Extension functions for NavController to handle navigation
 *
 * Note: Animations are now handled globally by the NavHost in SplitExpensesNavHost.kt
 * These functions provide a clean API for navigation without needing to specify animations
 * at each call site, since the NavHost handles all transitions consistently.
 */

/**
 * Navigate to a destination with slide animations
 * The actual animations are defined in the NavHost
 */
fun NavController.navigateWithSlideAnimation(route: String) {
    navigate(route)
}

/**
 * Navigate to a destination with custom options and slide animations
 * The actual animations are defined in the NavHost
 */
fun NavController.navigateWithSlideAnimation(
    route: String,
    builder: NavOptionsBuilder.() -> Unit
) {
    navigate(route, builder)
}

/**
 * Legacy functions for backward compatibility
 * These now rely on the NavHost's global animations
 */
fun NavController.navigateWithoutAnimation(route: String) {
    navigate(route)
}

fun NavController.navigateWithoutAnimation(
    route: String,
    builder: NavOptionsBuilder.() -> Unit
) {
    navigate(route, builder)
}

/**
 * Pop back stack - animations handled by NavHost
 */
fun NavController.popBackStackWithoutAnimation() {
    popBackStack()
}
