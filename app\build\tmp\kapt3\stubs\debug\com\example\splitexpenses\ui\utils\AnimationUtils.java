package com.example.splitexpenses.ui.utils;

/**
 * Animation utilities for optimizing performance during screen transitions
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\u0007\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0007\u00a8\u0006\f"}, d2 = {"Lcom/example/splitexpenses/ui/utils/AnimationUtils;", "", "()V", "emphasisAnimationSpec", "Landroidx/compose/animation/core/AnimationSpec;", "", "getEmphasisAnimationSpec", "()Landroidx/compose/animation/core/AnimationSpec;", "navigationAnimationSpec", "getNavigationAnimationSpec", "quickAnimationSpec", "getQuickAnimationSpec", "app_debug"})
public final class AnimationUtils {
    
    /**
     * Standard animation spec for navigation transitions
     * Optimized for smooth performance across different devices
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<java.lang.Float> navigationAnimationSpec = null;
    
    /**
     * Fast animation spec for quick UI updates
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<java.lang.Float> quickAnimationSpec = null;
    
    /**
     * Slow animation spec for emphasis
     */
    @org.jetbrains.annotations.NotNull()
    private static final androidx.compose.animation.core.AnimationSpec<java.lang.Float> emphasisAnimationSpec = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.splitexpenses.ui.utils.AnimationUtils INSTANCE = null;
    
    private AnimationUtils() {
        super();
    }
    
    /**
     * Standard animation spec for navigation transitions
     * Optimized for smooth performance across different devices
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> getNavigationAnimationSpec() {
        return null;
    }
    
    /**
     * Fast animation spec for quick UI updates
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> getQuickAnimationSpec() {
        return null;
    }
    
    /**
     * Slow animation spec for emphasis
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> getEmphasisAnimationSpec() {
        return null;
    }
}