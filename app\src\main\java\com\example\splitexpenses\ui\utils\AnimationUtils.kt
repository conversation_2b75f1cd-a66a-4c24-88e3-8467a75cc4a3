package com.example.splitexpenses.ui.utils

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import kotlinx.coroutines.delay

/**
 * Animation utilities for optimizing performance during screen transitions
 */
object AnimationUtils {
    
    /**
     * Standard animation spec for navigation transitions
     * Optimized for smooth performance across different devices
     */
    val navigationAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = 400,
        easing = FastOutSlowInEasing
    )
    
    /**
     * Fast animation spec for quick UI updates
     */
    val quickAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = 200,
        easing = FastOutSlowInEasing
    )
    
    /**
     * Slow animation spec for emphasis
     */
    val emphasisAnimationSpec: AnimationSpec<Float> = tween(
        durationMillis = 600,
        easing = FastOutSlowInEasing
    )
}

/**
 * Composable that defers heavy operations until after navigation animations complete
 * This helps prevent choppy animations by reducing CPU load during transitions
 */
@Composable
fun DeferredContent(
    delayMs: Long = 450, // Slightly longer than navigation animation
    content: @Composable () -> Unit
) {
    var shouldShowContent by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(delayMs)
        shouldShowContent = true
    }
    
    if (shouldShowContent) {
        content()
    }
}

/**
 * Composable that provides a loading placeholder during navigation transitions
 * Helps maintain smooth animations by showing lightweight content initially
 */
@Composable
fun TransitionOptimizedContent(
    delayMs: Long = 450,
    placeholder: @Composable () -> Unit = {},
    content: @Composable () -> Unit
) {
    var showFullContent by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        delay(delayMs)
        showFullContent = true
    }
    
    if (showFullContent) {
        content()
    } else {
        placeholder()
    }
}
